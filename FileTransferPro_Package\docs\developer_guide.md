# File Transfer Application - Developer Guide

## Architecture Overview

The File Transfer Application follows a modular architecture with clear separation of concerns:

```
FileTransfer/
├── src/
│   ├── core/           # Core networking and transfer logic
│   ├── gui/            # User interface components
│   └── utils/          # Shared utilities
├── tests/              # Test suite
├── docs/               # Documentation
└── main.py             # Application entry point
```

## Core Components

### 1. Core Layer (`src/core/`)

#### FileTransferServer (`server.py`)
- TCP server for receiving files
- Handles multiple concurrent connections
- Manages file reception and storage
- Provides callbacks for GUI integration

**Key Methods:**
- `start()`: Start the server and listen for connections
- `stop()`: Stop the server and close connections
- `_handle_client()`: Handle individual client connections
- `_receive_file()`: Receive file content from client

#### FileTransferClient (`client.py`)
- TCP client for sending files
- Connects to remote servers
- Handles file transmission with progress tracking
- Calculates checksums for integrity verification

**Key Methods:**
- `connect()`: Connect to a remote server
- `send_file()`: Send a file to the connected server
- `disconnect()`: Close the connection
- `_send_file_content()`: Send file data in chunks

#### FileTransferProtocol (`protocol.py`)
- Defines the communication protocol
- Message types and data structures
- Serialization and deserialization
- Protocol versioning

**Key Classes:**
- `MessageType`: Enumeration of message types
- `FileMetadata`: File information structure
- `TransferProgress`: Progress tracking data
- `FileTransferProtocol`: Protocol utilities

#### EncryptionManager (`encryption.py`)
- AES-256 encryption for file content
- RSA key exchange for secure communication
- File integrity verification
- Key derivation from passwords

**Key Methods:**
- `encrypt_data()`: Encrypt data using AES
- `decrypt_data()`: Decrypt data using AES
- `encrypt_aes_key()`: Encrypt AES key with RSA
- `decrypt_aes_key()`: Decrypt AES key with RSA

### 2. GUI Layer (`src/gui/`)

#### MainWindow (`main_window.py`)
- Main application window with tabbed interface
- Integrates all GUI components
- Handles user interactions
- Manages application state

**Key Features:**
- Send Files tab for client operations
- Receive Files tab for server operations
- Settings tab for configuration
- Real-time status updates and logging

#### ProgressDialog (`progress_dialog.py`)
- Detailed progress tracking dialog
- Transfer statistics and ETA calculation
- Speed monitoring
- Cancellation support

#### SettingsDialog (`settings_dialog.py`)
- Configuration interface
- Network, security, and general settings
- Input validation
- Settings persistence

### 3. Utils Layer (`src/utils/`)

#### FileUtils (`file_utils.py`)
- File operations and utilities
- Size formatting and checksum calculation
- Filename sanitization and safety checks
- Directory management

#### NetworkUtils (`network_utils.py`)
- Network-related utilities
- IP address and port validation
- Network interface discovery
- Connection testing

#### Logger (`logger.py`)
- Centralized logging configuration
- Console and file output
- Configurable log levels
- Thread-safe logging

## Protocol Specification

### Message Format

All messages follow this structure:

```
[Header: 8 bytes][Payload: Variable]
```

**Header Format:**
- Bytes 0-3: Message type ID (big-endian uint32)
- Bytes 4-7: Payload length (big-endian uint32)

**Payload Format:**
- JSON-encoded message data
- UTF-8 encoding

### Message Types

1. **FILE_METADATA**: File information exchange
2. **FILE_CHUNK**: File data transfer
3. **TRANSFER_COMPLETE**: Transfer completion notification
4. **TRANSFER_ERROR**: Error reporting
5. **HEARTBEAT**: Connection keep-alive
6. **DISCONNECT**: Connection termination

### File Transfer Flow

1. **Connection Establishment**
   - Client connects to server
   - Optional key exchange for encryption

2. **File Metadata Exchange**
   - Client sends file metadata (name, size, checksum)
   - Server validates and prepares for reception

3. **File Data Transfer**
   - Client sends file in chunks
   - Server receives and writes chunks
   - Progress updates sent periodically

4. **Transfer Completion**
   - Server verifies file integrity
   - Success/failure response sent to client
   - Connection maintained for additional transfers

## Development Setup

### Prerequisites

- Python 3.7 or higher
- pip package manager
- Git (for version control)

### Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd FileTransfer
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Run tests**:
   ```bash
   python -m pytest tests/ -v
   ```

### Development Workflow

1. **Create feature branch**:
   ```bash
   git checkout -b feature/new-feature
   ```

2. **Make changes and test**:
   ```bash
   python -m pytest tests/
   python main.py  # Test GUI
   ```

3. **Code formatting**:
   ```bash
   black src/ tests/
   flake8 src/ tests/
   ```

4. **Commit and push**:
   ```bash
   git add .
   git commit -m "Add new feature"
   git push origin feature/new-feature
   ```

## Testing

### Test Structure

- `tests/test_core.py`: Core functionality tests
- `tests/test_gui.py`: GUI component tests (to be implemented)
- `tests/test_utils.py`: Utility function tests (to be implemented)

### Running Tests

```bash
# Run all tests
python -m pytest tests/ -v

# Run specific test file
python -m pytest tests/test_core.py -v

# Run with coverage
python -m pytest tests/ --cov=src --cov-report=html
```

### Test Categories

1. **Unit Tests**: Individual component testing
2. **Integration Tests**: Component interaction testing
3. **End-to-End Tests**: Full workflow testing
4. **Performance Tests**: Speed and memory usage testing

## Adding New Features

### 1. Core Functionality

To add new core features:

1. **Define interfaces** in appropriate modules
2. **Implement functionality** with proper error handling
3. **Add comprehensive tests**
4. **Update protocol** if needed
5. **Document changes**

### 2. GUI Components

To add new GUI features:

1. **Create new widget classes** in `src/gui/`
2. **Integrate with MainWindow**
3. **Add event handlers and callbacks**
4. **Test user interactions**
5. **Update user documentation**

### 3. Protocol Extensions

To extend the protocol:

1. **Add new message types** to `MessageType` enum
2. **Define message structures**
3. **Update protocol handlers**
4. **Maintain backward compatibility**
5. **Version the protocol**

## Performance Optimization

### Network Performance

1. **Chunk Size Tuning**:
   - Larger chunks for high-bandwidth networks
   - Smaller chunks for unreliable connections
   - Adaptive chunk sizing based on network conditions

2. **Connection Management**:
   - Connection pooling for multiple transfers
   - Keep-alive mechanisms
   - Graceful connection handling

3. **Buffer Management**:
   - Efficient memory usage
   - Streaming for large files
   - Garbage collection optimization

### GUI Performance

1. **Threading**:
   - Background operations in separate threads
   - Non-blocking GUI updates
   - Thread-safe communication

2. **Progress Updates**:
   - Throttled update frequency
   - Efficient progress calculation
   - Minimal GUI redraws

## Security Considerations

### Encryption Implementation

1. **AES-256-GCM**: Authenticated encryption for file content
2. **RSA-2048**: Key exchange and digital signatures
3. **PBKDF2**: Key derivation from passwords
4. **Secure Random**: Cryptographically secure random number generation

### Security Best Practices

1. **Input Validation**: Sanitize all user inputs
2. **Path Traversal Prevention**: Validate file paths
3. **Memory Management**: Clear sensitive data from memory
4. **Error Handling**: Avoid information leakage in errors

## Deployment

### Packaging

1. **PyInstaller**: Create standalone executables
   ```bash
   pip install pyinstaller
   pyinstaller --onefile --windowed main.py
   ```

2. **Docker**: Containerized deployment
   ```dockerfile
   FROM python:3.9-slim
   COPY . /app
   WORKDIR /app
   RUN pip install -r requirements.txt
   CMD ["python", "main.py"]
   ```

### Distribution

1. **GitHub Releases**: Version-tagged releases
2. **Package Repositories**: PyPI distribution
3. **Platform-specific**: Windows MSI, macOS DMG, Linux packages

## Contributing

### Code Style

- Follow PEP 8 guidelines
- Use type hints where appropriate
- Write comprehensive docstrings
- Maintain consistent naming conventions

### Pull Request Process

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Update documentation
5. Submit pull request with description

### Issue Reporting

- Use GitHub Issues for bug reports
- Provide detailed reproduction steps
- Include system information
- Attach relevant log files

## Future Enhancements

### Planned Features

1. **Resume Capability**: Resume interrupted transfers
2. **Bandwidth Limiting**: Control transfer speed
3. **Directory Synchronization**: Sync entire directories
4. **Mobile Support**: Android/iOS applications
5. **Web Interface**: Browser-based file transfer

### Architecture Improvements

1. **Plugin System**: Extensible architecture
2. **Configuration Management**: Advanced settings
3. **Monitoring**: Performance metrics and analytics
4. **Clustering**: Distributed file transfer
