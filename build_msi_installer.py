#!/usr/bin/env python3
"""
MSI Installer Builder for FileTransfer Pro
Creates a complete MSI installer package with all dependencies.
"""

import os
import sys
import shutil
import subprocess
import tempfile
from pathlib import Path
from cx_Freeze import setup, Executable

# Application metadata
APP_NAME = "FileTransfer Pro"
APP_VERSION = "2.0.0"
APP_DESCRIPTION = "Professional File Transfer Application"
APP_AUTHOR = "FileTransfer Team"
APP_URL = "https://github.com/filetransfer/app"
APP_GUID = "{12345678-1234-5678-9012-123456789012}"

def check_requirements():
    """Check if all required tools are available."""
    print("🔍 Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        return False
    
    # Check cx_Freeze
    try:
        import cx_Freeze
        print(f"✓ cx_Freeze {cx_Freeze.version} found")
    except ImportError:
        print("❌ cx_Freeze not found. Install with: pip install cx_Freeze")
        return False
    
    # Check if main.py exists
    if not os.path.exists("main.py"):
        print("❌ main.py not found in current directory")
        return False
    
    print("✅ All requirements satisfied")
    return True

def clean_build_dirs():
    """Clean previous build directories."""
    print("🧹 Cleaning build directories...")
    
    dirs_to_clean = ["build", "dist"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ Cleaned {dir_name}/")

def create_icon():
    """Create a simple icon file if none exists."""
    icon_path = "icon.ico"
    if not os.path.exists(icon_path):
        print("ℹ️  No icon.ico found, creating a simple one...")
        # For now, we'll proceed without an icon
        # In a real scenario, you'd want to include a proper icon
        return None
    return icon_path

def get_include_files():
    """Get list of files to include in the build."""
    include_files = []
    
    # Include source directory
    if os.path.exists("src"):
        include_files.append(("src/", "src/"))
    
    # Include documentation
    if os.path.exists("docs"):
        include_files.append(("docs/", "docs/"))
    
    # Include README and requirements
    for file in ["README.md", "requirements.txt"]:
        if os.path.exists(file):
            include_files.append((file, file))
    
    return include_files

def build_executable():
    """Build the executable using cx_Freeze."""
    print("🔨 Building executable...")
    
    icon_path = create_icon()
    include_files = get_include_files()
    
    # Build options
    build_exe_options = {
        "packages": [
            "tkinter", "tkinter.ttk", "tkinter.filedialog", "tkinter.messagebox",
            "cryptography", "socket", "threading", "json", "pathlib", "logging",
            "argparse", "time", "datetime", "os", "sys", "shutil", "gzip", "zlib",
            "hashlib", "uuid", "queue", "collections", "functools", "itertools",
            "psutil", "tqdm"
        ],
        "excludes": ["test", "tests", "unittest", "pydoc", "doctest"],
        "include_files": include_files,
        "include_msvcrt": True,
        "optimize": 2,
        "zip_include_packages": ["*"],
        "zip_exclude_packages": []
    }
    
    # MSI build options
    bdist_msi_options = {
        "upgrade_code": APP_GUID,
        "add_to_path": False,
        "initial_target_dir": r"[ProgramFilesFolder]\FileTransfer Pro",
        "install_icon": icon_path,
        "summary_data": {
            "author": APP_AUTHOR,
            "comments": APP_DESCRIPTION,
            "keywords": "file transfer, networking, secure, professional"
        },
        "target_name": "FileTransferPro_Setup.msi"
    }
    
    # Executable configuration
    executables = [
        Executable(
            "main.py",
            base="Win32GUI",  # Use Win32GUI for windowed application
            target_name="FileTransferPro.exe",
            icon=icon_path,
            shortcut_name="FileTransfer Pro",
            shortcut_dir="DesktopFolder"
        )
    ]
    
    # Temporarily modify sys.argv for cx_Freeze
    original_argv = sys.argv[:]
    sys.argv = ["build_msi_installer.py", "bdist_msi"]
    
    try:
        setup(
            name=APP_NAME,
            version=APP_VERSION,
            description=APP_DESCRIPTION,
            author=APP_AUTHOR,
            url=APP_URL,
            options={
                "build_exe": build_exe_options,
                "bdist_msi": bdist_msi_options
            },
            executables=executables
        )
        print("✅ Executable built successfully")
        return True
    except Exception as e:
        print(f"❌ Build failed: {e}")
        return False
    finally:
        sys.argv = original_argv

def copy_msi_to_desktop():
    """Copy the generated MSI to the desktop."""
    print("📋 Copying MSI to desktop...")
    
    # Find the generated MSI file
    dist_dir = Path("dist")
    msi_files = list(dist_dir.glob("*.msi"))
    
    if not msi_files:
        print("❌ No MSI file found in dist/ directory")
        return False
    
    msi_file = msi_files[0]
    
    # Get desktop path
    try:
        import winshell
        desktop = Path(winshell.desktop())
    except ImportError:
        # Fallback to standard Windows desktop path
        desktop = Path.home() / "Desktop"
    
    if not desktop.exists():
        print(f"❌ Desktop directory not found: {desktop}")
        return False
    
    # Copy MSI to desktop
    desktop_msi = desktop / f"FileTransferPro_Setup_v{APP_VERSION}.msi"
    try:
        shutil.copy2(msi_file, desktop_msi)
        print(f"✅ MSI installer copied to: {desktop_msi}")
        return True
    except Exception as e:
        print(f"❌ Failed to copy MSI to desktop: {e}")
        return False

def create_installation_guide():
    """Create an installation guide."""
    guide_content = f"""# FileTransfer Pro v{APP_VERSION} - Installation Guide

## Installation

1. **Run the MSI Installer**:
   - Double-click `FileTransferPro_Setup_v{APP_VERSION}.msi`
   - Follow the installation wizard
   - The application will be installed to: `C:\\Program Files\\FileTransfer Pro\\`

2. **Desktop Shortcut**:
   - A desktop shortcut will be created automatically
   - Double-click the shortcut to launch the application

## Usage

### Send Files:
1. Start FileTransfer Pro
2. Click "Connect" to connect to a receiver
3. Add files or folders using the file picker
4. Click "Send Files" to start the transfer

### Receive Files:
1. Start FileTransfer Pro
2. Switch to "Receive Files" mode
3. Click "Start Server"
4. Share your IP address with the sender

## Features

- 🚀 Fast TCP-based transfers with automatic chunking
- 🎨 Modern, intuitive GUI with drag-drop support
- 📊 Real-time progress tracking with speed and ETA
- 🔒 Secure transfers with AES encryption
- 📁 Multiple file and folder support
- ⚡ Resume capability for interrupted transfers
- 🌐 Cross-platform compatibility

## System Requirements

- Windows 10 or later
- No Python installation required (standalone executable)
- Network connection for file transfers

## Troubleshooting

- **Firewall Issues**: Allow FileTransfer Pro through Windows Firewall
- **Connection Problems**: Check IP addresses and port availability
- **Transfer Errors**: Ensure sufficient disk space on both machines

## Support

For support and documentation, visit the docs/ folder in the installation directory.
"""
    
    try:
        with open("Installation_Guide.txt", "w", encoding="utf-8") as f:
            f.write(guide_content)
        print("✅ Installation guide created: Installation_Guide.txt")
        return True
    except Exception as e:
        print(f"❌ Failed to create installation guide: {e}")
        return False

def main():
    """Main build function."""
    print("🚀 FileTransfer Pro - MSI Installer Builder")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        print("❌ Requirements check failed!")
        return 1
    
    # Clean previous builds
    clean_build_dirs()
    
    # Build executable and MSI
    if not build_executable():
        print("❌ Build failed!")
        return 1
    
    # Copy MSI to desktop
    copy_msi_to_desktop()
    
    # Create installation guide
    create_installation_guide()
    
    print("\n🎉 MSI Installer created successfully!")
    print("\n📦 Generated files:")
    print("  • MSI Installer: dist/FileTransferPro_Setup.msi")
    print("  • Desktop Copy: ~/Desktop/FileTransferPro_Setup_v2.0.0.msi")
    print("  • Installation Guide: Installation_Guide.txt")
    
    print("\n📋 Next steps:")
    print("  1. Test the MSI installer on a clean machine")
    print("  2. Distribute the MSI file to users")
    print("  3. Users can install without Python or dependencies")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
