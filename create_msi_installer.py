#!/usr/bin/env python3
"""
Complete MSI Installer Creation Script for FileTransfer Pro
Automates the entire process from source code to distributable MSI installer.
"""

import os
import sys
import shutil
import subprocess
import tempfile
import json
from pathlib import Path
from datetime import datetime

class MSIInstallerBuilder:
    """Complete MSI installer builder for FileTransfer Pro."""
    
    def __init__(self):
        self.app_name = "FileTransfer Pro"
        self.app_version = "2.0.0"
        self.app_description = "Professional File Transfer Application"
        self.app_author = "FileTransfer Team"
        self.app_guid = "{12345678-1234-5678-9012-123456789012}"
        self.build_dir = Path("build_msi")
        self.dist_dir = Path("dist")
        
    def log(self, message, level="INFO"):
        """Log a message with timestamp."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def check_prerequisites(self):
        """Check all prerequisites for building."""
        self.log("Checking prerequisites...")
        
        # Check Python version
        if sys.version_info < (3, 7):
            self.log("Python 3.7 or higher is required", "ERROR")
            return False
        
        # Check required packages
        required_packages = ["cx_Freeze"]
        for package in required_packages:
            try:
                __import__(package)
                self.log(f"✓ {package} found")
            except ImportError:
                self.log(f"Installing {package}...", "INFO")
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                    self.log(f"✓ {package} installed")
                except subprocess.CalledProcessError:
                    self.log(f"Failed to install {package}", "ERROR")
                    return False
        
        # Check main application file
        if not os.path.exists("main.py"):
            self.log("main.py not found in current directory", "ERROR")
            return False
        
        self.log("✅ All prerequisites satisfied")
        return True
    
    def clean_build_directories(self):
        """Clean previous build directories."""
        self.log("Cleaning build directories...")

        dirs_to_clean = [self.build_dir, self.dist_dir, Path("build")]
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                self.log(f"✓ Cleaned {dir_path}/")

        # Create fresh directories
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)
    
    def create_setup_script(self):
        """Create the cx_Freeze setup script."""
        self.log("Creating setup script...")
        
        setup_content = f'''#!/usr/bin/env python3
"""
Generated setup script for FileTransfer Pro MSI installer.
"""

import os
import sys
from cx_Freeze import setup, Executable

# Application metadata
APP_NAME = "{self.app_name}"
APP_VERSION = "{self.app_version}"
APP_DESCRIPTION = "{self.app_description}"
APP_AUTHOR = "{self.app_author}"
APP_GUID = "{self.app_guid}"

# Build options
build_exe_options = {{
    "packages": [
        "tkinter", "tkinter.ttk", "tkinter.filedialog", "tkinter.messagebox",
        "cryptography", "socket", "threading", "json", "pathlib", "logging",
        "argparse", "time", "datetime", "os", "sys", "shutil", "gzip", "zlib",
        "hashlib", "uuid", "queue", "collections", "functools", "itertools",
        "psutil", "tqdm", "ssl", "urllib", "http", "email", "base64"
    ],
    "excludes": ["test", "tests", "unittest", "pydoc", "doctest", "tkinter.test"],
    "include_files": [
        ("src/", "src/"),
        ("docs/", "docs/") if os.path.exists("docs") else None,
        ("README.md", "README.md") if os.path.exists("README.md") else None,
        ("requirements.txt", "requirements.txt") if os.path.exists("requirements.txt") else None
    ],
    "include_msvcrt": True,
    "optimize": 2,
    "zip_include_packages": ["*"],
    "zip_exclude_packages": [],
    "silent": True
}}

# Remove None entries from include_files
build_exe_options["include_files"] = [f for f in build_exe_options["include_files"] if f is not None]

# MSI build options
bdist_msi_options = {{
    "upgrade_code": APP_GUID,
    "add_to_path": False,
    "initial_target_dir": r"[ProgramFilesFolder]\\FileTransfer Pro",
    "summary_data": {{
        "author": APP_AUTHOR,
        "comments": APP_DESCRIPTION,
        "keywords": "file transfer, networking, secure, professional"
    }},
    "target_name": "FileTransferPro_Setup.msi"
}}

# Executable configuration
executables = [
    Executable(
        "main.py",
        base="Win32GUI",
        target_name="FileTransferPro.exe",
        shortcut_name="FileTransfer Pro",
        shortcut_dir="DesktopFolder"
    )
]

if __name__ == "__main__":
    setup(
        name=APP_NAME,
        version=APP_VERSION,
        description=APP_DESCRIPTION,
        author=APP_AUTHOR,
        options={{
            "build_exe": build_exe_options,
            "bdist_msi": bdist_msi_options
        }},
        executables=executables
    )
'''
        
        setup_script_path = self.build_dir / "setup_msi.py"
        with open(setup_script_path, "w", encoding="utf-8") as f:
            f.write(setup_content)
        
        self.log(f"✓ Setup script created: {setup_script_path}")
        return setup_script_path
    
    def build_msi(self, setup_script_path):
        """Build the MSI installer."""
        self.log("Building MSI installer...")
        
        try:
            # Run cx_Freeze to build MSI
            cmd = [sys.executable, str(setup_script_path), "bdist_msi"]
            result = subprocess.run(
                cmd,
                cwd=os.getcwd(),
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode == 0:
                self.log("✅ MSI installer built successfully")
                return True
            else:
                self.log(f"Build failed with return code {result.returncode}", "ERROR")
                self.log(f"STDOUT: {result.stdout}", "DEBUG")
                self.log(f"STDERR: {result.stderr}", "ERROR")
                return False
                
        except subprocess.TimeoutExpired:
            self.log("Build process timed out", "ERROR")
            return False
        except Exception as e:
            self.log(f"Build process failed: {e}", "ERROR")
            return False
    
    def copy_to_desktop(self):
        """Copy the MSI installer to the desktop."""
        self.log("Copying MSI to desktop...")
        
        # Find the generated MSI file
        msi_files = list(self.dist_dir.glob("*.msi"))
        if not msi_files:
            self.log("No MSI file found in dist/ directory", "ERROR")
            return False
        
        msi_file = msi_files[0]
        
        # Get desktop path
        try:
            import winshell
            desktop = Path(winshell.desktop())
        except ImportError:
            # Fallback to standard Windows desktop path
            desktop = Path.home() / "Desktop"
        
        if not desktop.exists():
            self.log(f"Desktop directory not found: {desktop}", "ERROR")
            return False
        
        # Copy MSI to desktop with versioned name
        desktop_msi = desktop / f"FileTransferPro_Setup_v{self.app_version}.msi"
        try:
            shutil.copy2(msi_file, desktop_msi)
            self.log(f"✅ MSI installer copied to: {desktop_msi}")
            return desktop_msi
        except Exception as e:
            self.log(f"Failed to copy MSI to desktop: {e}", "ERROR")
            return False
    
    def create_documentation(self):
        """Create installation and usage documentation."""
        self.log("Creating documentation...")
        
        # Installation guide
        install_guide = f"""# FileTransfer Pro v{self.app_version} - Installation Guide

## Quick Installation

1. **Download**: Get `FileTransferPro_Setup_v{self.app_version}.msi`
2. **Install**: Double-click the MSI file and follow the wizard
3. **Launch**: Use the desktop shortcut or Start Menu

## Installation Details

- **Installation Path**: `C:\\Program Files\\FileTransfer Pro\\`
- **Desktop Shortcut**: Created automatically
- **Start Menu**: Added to Programs list
- **Requirements**: Windows 10+ (no Python needed)

## First Use

### Send Files:
1. Launch FileTransfer Pro
2. Click "Connect" tab
3. Enter receiver's IP address
4. Add files and click "Send"

### Receive Files:
1. Launch FileTransfer Pro  
2. Click "Receive Files" tab
3. Click "Start Server"
4. Share your IP with sender

## Features

✅ Professional GUI with modern design
✅ Secure file transfers with encryption
✅ Resume interrupted transfers
✅ Real-time progress and speed monitoring
✅ Multiple file and folder support
✅ No size limits on transfers
✅ Cross-platform compatibility

## Troubleshooting

**Connection Issues:**
- Check Windows Firewall settings
- Verify IP addresses are correct
- Ensure both machines are on same network

**Performance:**
- Close other network applications
- Use wired connection for best speed
- Check available disk space

## Uninstall

Use Windows "Add or Remove Programs" to uninstall cleanly.
"""
        
        try:
            with open("FileTransfer_Pro_Installation_Guide.txt", "w", encoding="utf-8") as f:
                f.write(install_guide)
            self.log("✓ Installation guide created")
            return True
        except Exception as e:
            self.log(f"Failed to create documentation: {e}", "ERROR")
            return False
    
    def build(self):
        """Execute the complete build process."""
        self.log(f"🚀 Starting MSI build for {self.app_name} v{self.app_version}")
        self.log("=" * 60)
        
        # Step 1: Check prerequisites
        if not self.check_prerequisites():
            return False
        
        # Step 2: Clean build directories
        self.clean_build_directories()
        
        # Step 3: Create setup script
        setup_script = self.create_setup_script()
        if not setup_script:
            return False
        
        # Step 4: Build MSI
        if not self.build_msi(setup_script):
            return False
        
        # Step 5: Copy to desktop
        desktop_msi = self.copy_to_desktop()
        
        # Step 6: Create documentation
        self.create_documentation()
        
        # Success summary
        self.log("🎉 MSI Installer created successfully!")
        self.log("=" * 60)
        self.log("📦 Generated Files:")
        self.log(f"  • MSI Installer: dist/FileTransferPro_Setup.msi")
        if desktop_msi:
            self.log(f"  • Desktop Copy: {desktop_msi}")
        self.log(f"  • Installation Guide: FileTransfer_Pro_Installation_Guide.txt")
        
        self.log("📋 Distribution Ready:")
        self.log("  • MSI can be distributed to any Windows machine")
        self.log("  • No Python installation required on target machines")
        self.log("  • Includes all dependencies and creates shortcuts")
        
        return True

def main():
    """Main entry point."""
    builder = MSIInstallerBuilder()
    success = builder.build()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
