#!/usr/bin/env python3
"""
Complete Installer Package Creator for FileTransfer Pro
Creates a distributable installer package using PyInstaller and NSIS.
"""

import os
import sys
import shutil
import subprocess
import tempfile
import json
from pathlib import Path
from datetime import datetime

class InstallerPackageBuilder:
    """Complete installer package builder for FileTransfer Pro."""
    
    def __init__(self):
        self.app_name = "FileTransfer Pro"
        self.app_version = "2.0.0"
        self.app_description = "Professional File Transfer Application"
        self.app_author = "FileTransfer Team"
        self.build_dir = Path("build_installer")
        self.dist_dir = Path("dist")
        self.package_dir = Path("FileTransferPro_Package")
        
    def log(self, message, level="INFO"):
        """Log a message with timestamp."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def check_prerequisites(self):
        """Check all prerequisites for building."""
        self.log("Checking prerequisites...")
        
        # Check Python version
        if sys.version_info < (3, 7):
            self.log("Python 3.7 or higher is required", "ERROR")
            return False
        
        # Check required packages
        required_packages = ["pyinstaller"]
        for package in required_packages:
            try:
                __import__(package)
                self.log(f"✓ {package} found")
            except ImportError:
                self.log(f"Installing {package}...", "INFO")
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                    self.log(f"✓ {package} installed")
                except subprocess.CalledProcessError:
                    self.log(f"Failed to install {package}", "ERROR")
                    return False
        
        # Check main application file
        if not os.path.exists("main.py"):
            self.log("main.py not found in current directory", "ERROR")
            return False
        
        self.log("✅ All prerequisites satisfied")
        return True
    
    def clean_build_directories(self):
        """Clean previous build directories."""
        self.log("Cleaning build directories...")
        
        dirs_to_clean = [self.build_dir, self.dist_dir, self.package_dir, Path("build")]
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                self.log(f"✓ Cleaned {dir_path}/")
        
        # Create fresh directories
        self.build_dir.mkdir(exist_ok=True)
        self.package_dir.mkdir(exist_ok=True)
    
    def create_executable(self):
        """Create standalone executable using PyInstaller."""
        self.log("Creating standalone executable...")
        
        try:
            # PyInstaller command using Python module
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--onefile",                    # Single executable file
                "--windowed",                   # No console window (for GUI)
                "--name=FileTransferPro",       # Executable name
                "--add-data=src;src",           # Include source directory
                "--hidden-import=tkinter",      # Ensure tkinter is included
                "--hidden-import=cryptography", # Ensure cryptography is included
                "--hidden-import=psutil",       # Ensure psutil is included
                "--hidden-import=tqdm",         # Ensure tqdm is included
                "--clean",                      # Clean cache
                "--distpath=dist",              # Output directory
                "main.py"                       # Main script
            ]

            # Add docs if available
            if os.path.exists("docs"):
                cmd.insert(-1, "--add-data=docs;docs")

            # Add icon if available
            if os.path.exists("icon.ico"):
                cmd.insert(-1, "--icon=icon.ico")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode == 0:
                self.log("✅ Executable created successfully")
                return True
            else:
                self.log(f"PyInstaller failed with return code {result.returncode}", "ERROR")
                self.log(f"STDERR: {result.stderr}", "ERROR")
                return False
                
        except subprocess.TimeoutExpired:
            self.log("PyInstaller process timed out", "ERROR")
            return False
        except Exception as e:
            self.log(f"PyInstaller process failed: {e}", "ERROR")
            return False
    
    def create_installer_package(self):
        """Create a complete installer package."""
        self.log("Creating installer package...")
        
        # Copy executable
        exe_name = "FileTransferPro.exe"
        exe_path = self.dist_dir / exe_name
        
        if not exe_path.exists():
            self.log(f"Executable not found: {exe_path}", "ERROR")
            return False
        
        shutil.copy2(exe_path, self.package_dir / exe_name)
        self.log(f"✓ Copied {exe_name}")
        
        # Copy documentation
        docs_to_copy = [
            "README.md",
            "requirements.txt"
        ]
        
        for doc in docs_to_copy:
            if os.path.exists(doc):
                shutil.copy2(doc, self.package_dir / Path(doc).name)
                self.log(f"✓ Copied {doc}")
        
        # Copy docs directory if it exists
        if os.path.exists("docs"):
            shutil.copytree("docs", self.package_dir / "docs")
            self.log("✓ Copied docs/ directory")
        
        # Create installer scripts
        self.create_installer_scripts()
        
        # Create package documentation
        self.create_package_documentation()
        
        return True
    
    def create_installer_scripts(self):
        """Create installer and uninstaller scripts."""
        self.log("Creating installer scripts...")
        
        # Windows installer script
        install_script = f'''@echo off
echo Installing {self.app_name} v{self.app_version}...

REM Create installation directory
set INSTALL_DIR=%ProgramFiles%\\{self.app_name}
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy files
copy "FileTransferPro.exe" "%INSTALL_DIR%\\"
copy "README.md" "%INSTALL_DIR%\\" 2>nul
copy "requirements.txt" "%INSTALL_DIR%\\" 2>nul
if exist "docs" xcopy "docs" "%INSTALL_DIR%\\docs\\" /E /I /Y

REM Create desktop shortcut
set DESKTOP=%USERPROFILE%\\Desktop
echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP%\\{self.app_name}.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\FileTransferPro.exe'; $Shortcut.Save()"

REM Create start menu entry
set STARTMENU=%ProgramData%\\Microsoft\\Windows\\Start Menu\\Programs
if not exist "%STARTMENU%\\{self.app_name}" mkdir "%STARTMENU%\\{self.app_name}"
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU%\\{self.app_name}\\{self.app_name}.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\FileTransferPro.exe'; $Shortcut.Save()"

REM Create uninstaller
echo @echo off > "%INSTALL_DIR%\\uninstall.bat"
echo echo Uninstalling {self.app_name}... >> "%INSTALL_DIR%\\uninstall.bat"
echo del "%DESKTOP%\\{self.app_name}.lnk" 2^>nul >> "%INSTALL_DIR%\\uninstall.bat"
echo rmdir /s /q "%STARTMENU%\\{self.app_name}" 2^>nul >> "%INSTALL_DIR%\\uninstall.bat"
echo rmdir /s /q "%INSTALL_DIR%" >> "%INSTALL_DIR%\\uninstall.bat"
echo echo {self.app_name} has been uninstalled. >> "%INSTALL_DIR%\\uninstall.bat"
echo pause >> "%INSTALL_DIR%\\uninstall.bat"

echo.
echo {self.app_name} has been installed successfully!
echo.
echo Installation directory: %INSTALL_DIR%
echo Desktop shortcut: %DESKTOP%\\{self.app_name}.lnk
echo.
echo You can now launch {self.app_name} from the desktop shortcut or Start Menu.
echo To uninstall, run: "%INSTALL_DIR%\\uninstall.bat"
echo.
pause
'''
        
        # Save installer script
        install_script_path = self.package_dir / "install.bat"
        with open(install_script_path, "w", encoding="utf-8") as f:
            f.write(install_script)
        self.log("✓ Created install.bat")
        
        # Portable launcher script
        portable_script = f'''@echo off
echo Starting {self.app_name} v{self.app_version} (Portable Mode)...
echo.
echo This is a portable version - no installation required.
echo All files will be kept in this directory.
echo.
start "" "FileTransferPro.exe"
'''
        
        portable_script_path = self.package_dir / "run_portable.bat"
        with open(portable_script_path, "w", encoding="utf-8") as f:
            f.write(portable_script)
        self.log("✓ Created run_portable.bat")
    
    def create_package_documentation(self):
        """Create comprehensive package documentation."""
        self.log("Creating package documentation...")
        
        readme_content = f"""# {self.app_name} v{self.app_version} - Installation Package

## Installation Options

### Option 1: Full Installation (Recommended)
1. **Run as Administrator**: Right-click `install.bat` and select "Run as administrator"
2. **Follow prompts**: The installer will copy files and create shortcuts
3. **Launch**: Use the desktop shortcut or Start Menu entry

### Option 2: Portable Mode
1. **No installation needed**: Just run `run_portable.bat`
2. **Keep files together**: Don't move or delete any files in this folder
3. **Launch**: Double-click `run_portable.bat` to start the application

## What Gets Installed

**Full Installation:**
- Program Files: `C:\\Program Files\\{self.app_name}\\`
- Desktop Shortcut: `{self.app_name}.lnk`
- Start Menu: Programs > {self.app_name}
- Uninstaller: Available in installation directory

**Portable Mode:**
- Everything runs from this folder
- No system changes
- No shortcuts created

## System Requirements

- Windows 10 or later (64-bit recommended)
- 100 MB free disk space
- Network connection for file transfers
- No Python installation required

## Quick Start Guide

### Send Files:
1. Launch {self.app_name}
2. Click "Connect" tab
3. Enter receiver's IP address and port
4. Add files using "Add Files" or "Add Folder"
5. Click "Send Files" to start transfer

### Receive Files:
1. Launch {self.app_name}
2. Click "Receive Files" tab
3. Click "Start Server"
4. Share your IP address with the sender
5. Files will be saved to your Downloads folder

## Features

✅ **Professional GUI** - Modern, intuitive interface
✅ **Secure Transfers** - AES encryption for all file data
✅ **Resume Capability** - Continue interrupted transfers
✅ **Real-time Progress** - Speed, ETA, and progress monitoring
✅ **Multiple Files** - Send files and folders simultaneously
✅ **No Size Limits** - Transfer files of any size
✅ **Cross-platform** - Works with Mac and Linux versions

## Troubleshooting

**Installation Issues:**
- Run `install.bat` as Administrator
- Disable antivirus temporarily during installation
- Check Windows User Account Control settings

**Connection Problems:**
- Check Windows Firewall settings
- Verify IP addresses are correct
- Ensure both machines are on the same network
- Try different port numbers if default fails

**Performance Issues:**
- Close other network applications
- Use wired connection for best speed
- Check available disk space on both machines

## Uninstallation

**Full Installation:**
- Run the uninstaller: `C:\\Program Files\\{self.app_name}\\uninstall.bat`
- Or use Windows "Add or Remove Programs"

**Portable Mode:**
- Simply delete this entire folder

## Support

For additional help and documentation:
- Check the `docs/` folder (if included)
- Visit the project repository
- Review the user guide for detailed instructions

## Version Information

- **Version**: {self.app_version}
- **Build Date**: {datetime.now().strftime("%Y-%m-%d")}
- **Platform**: Windows (64-bit)
- **Dependencies**: All included (no Python required)

---

Thank you for using {self.app_name}!
"""
        
        try:
            readme_path = self.package_dir / "README_INSTALLATION.txt"
            with open(readme_path, "w", encoding="utf-8") as f:
                f.write(readme_content)
            self.log("✓ Created README_INSTALLATION.txt")
            return True
        except Exception as e:
            self.log(f"Failed to create package documentation: {e}", "ERROR")
            return False
    
    def copy_to_desktop(self):
        """Copy the installer package to the desktop."""
        self.log("Copying installer package to desktop...")
        
        try:
            import winshell
            desktop = Path(winshell.desktop())
        except ImportError:
            desktop = Path.home() / "Desktop"
        
        if not desktop.exists():
            self.log(f"Desktop directory not found: {desktop}", "ERROR")
            return False
        
        # Create a zip file of the package
        desktop_package = desktop / f"FileTransferPro_v{self.app_version}_Installer"
        
        try:
            shutil.copytree(self.package_dir, desktop_package)
            self.log(f"✅ Installer package copied to: {desktop_package}")
            return desktop_package
        except Exception as e:
            self.log(f"Failed to copy package to desktop: {e}", "ERROR")
            return False
    
    def build(self):
        """Execute the complete build process."""
        self.log(f"🚀 Starting installer build for {self.app_name} v{self.app_version}")
        self.log("=" * 70)
        
        # Step 1: Check prerequisites
        if not self.check_prerequisites():
            return False
        
        # Step 2: Clean build directories
        self.clean_build_directories()
        
        # Step 3: Create executable
        if not self.create_executable():
            return False
        
        # Step 4: Create installer package
        if not self.create_installer_package():
            return False
        
        # Step 5: Copy to desktop
        desktop_package = self.copy_to_desktop()
        
        # Success summary
        self.log("🎉 Installer Package created successfully!")
        self.log("=" * 70)
        self.log("📦 Generated Package:")
        self.log(f"  • Local Package: {self.package_dir}/")
        if desktop_package:
            self.log(f"  • Desktop Copy: {desktop_package}/")
        
        self.log("📋 Distribution Options:")
        self.log("  • Full Installation: Run install.bat as Administrator")
        self.log("  • Portable Mode: Run run_portable.bat (no installation)")
        self.log("  • Package can be distributed as a ZIP file")
        self.log("  • No Python installation required on target machines")
        
        return True

def main():
    """Main entry point."""
    builder = InstallerPackageBuilder()
    success = builder.build()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
