# FileTransfer Pro v2.0.0 - Installation Package

## Installation Options

### Option 1: Full Installation (Recommended)
1. **Run as Administrator**: Right-click `install.bat` and select "Run as administrator"
2. **Follow prompts**: The installer will copy files and create shortcuts
3. **Launch**: Use the desktop shortcut or Start Menu entry

### Option 2: Portable Mode
1. **No installation needed**: Just run `run_portable.bat`
2. **Keep files together**: Don't move or delete any files in this folder
3. **Launch**: Double-click `run_portable.bat` to start the application

## What Gets Installed

**Full Installation:**
- Program Files: `C:\Program Files\FileTransfer Pro\`
- Desktop Shortcut: `FileTransfer Pro.lnk`
- Start Menu: Programs > FileTransfer Pro
- Uninstaller: Available in installation directory

**Portable Mode:**
- Everything runs from this folder
- No system changes
- No shortcuts created

## System Requirements

- Windows 10 or later (64-bit recommended)
- 100 MB free disk space
- Network connection for file transfers
- No Python installation required

## Quick Start Guide

### Send Files:
1. Launch FileTransfer Pro
2. Click "Connect" tab
3. Enter receiver's IP address and port
4. Add files using "Add Files" or "Add Folder"
5. Click "Send Files" to start transfer

### Receive Files:
1. Launch FileTransfer Pro
2. Click "Receive Files" tab
3. Click "Start Server"
4. Share your IP address with the sender
5. Files will be saved to your Downloads folder

## Features

✅ **Professional GUI** - Modern, intuitive interface
✅ **Secure Transfers** - AES encryption for all file data
✅ **Resume Capability** - Continue interrupted transfers
✅ **Real-time Progress** - Speed, ETA, and progress monitoring
✅ **Multiple Files** - Send files and folders simultaneously
✅ **No Size Limits** - Transfer files of any size
✅ **Cross-platform** - Works with Mac and Linux versions

## Troubleshooting

**Installation Issues:**
- Run `install.bat` as Administrator
- Disable antivirus temporarily during installation
- Check Windows User Account Control settings

**Connection Problems:**
- Check Windows Firewall settings
- Verify IP addresses are correct
- Ensure both machines are on the same network
- Try different port numbers if default fails

**Performance Issues:**
- Close other network applications
- Use wired connection for best speed
- Check available disk space on both machines

## Uninstallation

**Full Installation:**
- Run the uninstaller: `C:\Program Files\FileTransfer Pro\uninstall.bat`
- Or use Windows "Add or Remove Programs"

**Portable Mode:**
- Simply delete this entire folder

## Support

For additional help and documentation:
- Check the `docs/` folder (if included)
- Visit the project repository
- Review the user guide for detailed instructions

## Version Information

- **Version**: 2.0.0
- **Build Date**: 2025-06-26
- **Platform**: Windows (64-bit)
- **Dependencies**: All included (no Python required)

---

Thank you for using FileTransfer Pro!
