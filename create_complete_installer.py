#!/usr/bin/env python3
"""
Complete Installer Creator for FileTransfer Pro
Creates both a package installer and attempts to create an MSI using available tools.
"""

import os
import sys
import shutil
import subprocess
import tempfile
import zipfile
from pathlib import Path
from datetime import datetime

class CompleteInstallerBuilder:
    """Complete installer builder with multiple output formats."""
    
    def __init__(self):
        self.app_name = "FileTransfer Pro"
        self.app_version = "2.0.0"
        self.app_description = "Professional File Transfer Application"
        self.package_dir = Path("FileTransferPro_Package")
        self.desktop = self.get_desktop_path()
        
    def get_desktop_path(self):
        """Get the desktop path."""
        try:
            import winshell
            return Path(winshell.desktop())
        except ImportError:
            return Path.home() / "Desktop"
    
    def log(self, message, level="INFO"):
        """Log a message with timestamp."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def create_zip_installer(self):
        """Create a ZIP file installer for easy distribution."""
        self.log("Creating ZIP installer...")
        
        if not self.package_dir.exists():
            self.log("Package directory not found. Run create_installer_package.py first.", "ERROR")
            return False
        
        zip_name = f"FileTransferPro_v{self.app_version}_Installer.zip"
        zip_path = self.desktop / zip_name
        
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in self.package_dir.rglob('*'):
                    if file_path.is_file():
                        arcname = file_path.relative_to(self.package_dir)
                        zipf.write(file_path, arcname)
            
            self.log(f"✅ ZIP installer created: {zip_path}")
            return zip_path
        except Exception as e:
            self.log(f"Failed to create ZIP installer: {e}", "ERROR")
            return False
    
    def create_self_extracting_installer(self):
        """Create a self-extracting installer using 7-Zip if available."""
        self.log("Attempting to create self-extracting installer...")
        
        # Check if 7-Zip is available
        seven_zip_paths = [
            r"C:\Program Files\7-Zip\7z.exe",
            r"C:\Program Files (x86)\7-Zip\7z.exe"
        ]
        
        seven_zip = None
        for path in seven_zip_paths:
            if os.path.exists(path):
                seven_zip = path
                break
        
        if not seven_zip:
            self.log("7-Zip not found. Skipping self-extracting installer.", "INFO")
            return False
        
        try:
            sfx_name = f"FileTransferPro_v{self.app_version}_Setup.exe"
            sfx_path = self.desktop / sfx_name
            
            # Create SFX config
            sfx_config = f"""
;!@Install@!UTF-8!
Title="{self.app_name} v{self.app_version} Setup"
BeginPrompt="This will install {self.app_name} v{self.app_version}. Continue?"
RunProgram="install.bat"
;!@InstallEnd@!
"""
            
            config_path = self.package_dir / "config.txt"
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(sfx_config)
            
            # Create self-extracting archive
            cmd = [
                seven_zip, "a", "-sfx7z.sfx", str(sfx_path),
                f"{self.package_dir}/*", f"-xr!{config_path.name}"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log(f"✅ Self-extracting installer created: {sfx_path}")
                return sfx_path
            else:
                self.log(f"Failed to create self-extracting installer: {result.stderr}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Error creating self-extracting installer: {e}", "ERROR")
            return False
        finally:
            # Clean up config file
            if config_path.exists():
                config_path.unlink()
    
    def create_inno_setup_script(self):
        """Create an Inno Setup script for MSI-like installer."""
        self.log("Creating Inno Setup script...")
        
        script_content = f'''[Setup]
AppName={self.app_name}
AppVersion={self.app_version}
AppPublisher=FileTransfer Team
AppPublisherURL=https://github.com/filetransfer/app
AppSupportURL=https://github.com/filetransfer/app
AppUpdatesURL=https://github.com/filetransfer/app
DefaultDirName={{autopf}}\\{self.app_name}
DefaultGroupName={self.app_name}
AllowNoIcons=yes
LicenseFile=
OutputDir=.
OutputBaseFilename=FileTransferPro_v{self.app_version}_Setup
SetupIconFile=
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{{cm:CreateDesktopIcon}}"; GroupDescription: "{{cm:AdditionalIcons}}"; Flags: unchecked

[Files]
Source: "{self.package_dir}\\FileTransferPro.exe"; DestDir: "{{app}}"; Flags: ignoreversion
Source: "{self.package_dir}\\README.md"; DestDir: "{{app}}"; Flags: ignoreversion
Source: "{self.package_dir}\\requirements.txt"; DestDir: "{{app}}"; Flags: ignoreversion
Source: "{self.package_dir}\\docs\\*"; DestDir: "{{app}}\\docs"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{{group}}\\{self.app_name}"; Filename: "{{app}}\\FileTransferPro.exe"
Name: "{{group}}\\{{cm:UninstallProgram,{self.app_name}}}"; Filename: "{{uninstallexe}}"
Name: "{{autodesktop}}\\{self.app_name}"; Filename: "{{app}}\\FileTransferPro.exe"; Tasks: desktopicon

[Run]
Filename: "{{app}}\\FileTransferPro.exe"; Description: "{{cm:LaunchProgram,{self.app_name}}}"; Flags: nowait postinstall skipifsilent
'''
        
        script_path = self.desktop / "FileTransferPro_Setup.iss"
        try:
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            self.log(f"✅ Inno Setup script created: {script_path}")
            self.log("ℹ️  To create MSI: Install Inno Setup and compile this script")
            return script_path
        except Exception as e:
            self.log(f"Failed to create Inno Setup script: {e}", "ERROR")
            return False
    
    def create_nsis_script(self):
        """Create an NSIS script for creating Windows installer."""
        self.log("Creating NSIS script...")
        
        script_content = f'''!define APP_NAME "{self.app_name}"
!define APP_VERSION "{self.app_version}"
!define APP_PUBLISHER "FileTransfer Team"
!define APP_EXE "FileTransferPro.exe"

Name "${{APP_NAME}} v${{APP_VERSION}}"
OutFile "FileTransferPro_v{self.app_version}_Setup.exe"
InstallDir "$PROGRAMFILES\\${{APP_NAME}}"
RequestExecutionLevel admin

Page directory
Page instfiles

Section "Install"
    SetOutPath $INSTDIR
    File "{self.package_dir}\\FileTransferPro.exe"
    File "{self.package_dir}\\README.md"
    File "{self.package_dir}\\requirements.txt"
    
    SetOutPath $INSTDIR\\docs
    File /r "{self.package_dir}\\docs\\*"
    
    CreateDirectory "$SMPROGRAMS\\${{APP_NAME}}"
    CreateShortCut "$SMPROGRAMS\\${{APP_NAME}}\\${{APP_NAME}}.lnk" "$INSTDIR\\${{APP_EXE}}"
    CreateShortCut "$DESKTOP\\${{APP_NAME}}.lnk" "$INSTDIR\\${{APP_EXE}}"
    
    WriteUninstaller "$INSTDIR\\Uninstall.exe"
    CreateShortCut "$SMPROGRAMS\\${{APP_NAME}}\\Uninstall.lnk" "$INSTDIR\\Uninstall.exe"
SectionEnd

Section "Uninstall"
    Delete "$INSTDIR\\${{APP_EXE}}"
    Delete "$INSTDIR\\README.md"
    Delete "$INSTDIR\\requirements.txt"
    RMDir /r "$INSTDIR\\docs"
    Delete "$INSTDIR\\Uninstall.exe"
    RMDir "$INSTDIR"
    
    Delete "$SMPROGRAMS\\${{APP_NAME}}\\${{APP_NAME}}.lnk"
    Delete "$SMPROGRAMS\\${{APP_NAME}}\\Uninstall.lnk"
    RMDir "$SMPROGRAMS\\${{APP_NAME}}"
    Delete "$DESKTOP\\${{APP_NAME}}.lnk"
SectionEnd
'''
        
        script_path = self.desktop / "FileTransferPro_Setup.nsi"
        try:
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            self.log(f"✅ NSIS script created: {script_path}")
            self.log("ℹ️  To create installer: Install NSIS and compile this script")
            return script_path
        except Exception as e:
            self.log(f"Failed to create NSIS script: {e}", "ERROR")
            return False
    
    def create_distribution_guide(self):
        """Create a comprehensive distribution guide."""
        self.log("Creating distribution guide...")
        
        guide_content = f"""# {self.app_name} v{self.app_version} - Distribution Guide

## Available Distribution Formats

### 1. ZIP Package (Ready to Use)
**File**: `FileTransferPro_v{self.app_version}_Installer.zip`
- **Best for**: Quick distribution, email sharing
- **Usage**: Extract and run `install.bat` (full install) or `run_portable.bat` (portable)
- **Size**: Smallest download
- **Requirements**: None

### 2. Self-Extracting Archive (If Available)
**File**: `FileTransferPro_v{self.app_version}_Setup.exe`
- **Best for**: Professional distribution
- **Usage**: Double-click to extract and install
- **Size**: Medium
- **Requirements**: 7-Zip installed on build machine

### 3. Inno Setup Installer Script
**File**: `FileTransferPro_Setup.iss`
- **Best for**: Creating professional MSI-like installers
- **Usage**: Compile with Inno Setup to create installer
- **Requirements**: Inno Setup (free download)
- **Output**: Professional Windows installer

### 4. NSIS Installer Script
**File**: `FileTransferPro_Setup.nsi`
- **Best for**: Creating lightweight Windows installers
- **Usage**: Compile with NSIS to create installer
- **Requirements**: NSIS (free download)
- **Output**: Small, fast Windows installer

## Recommended Distribution Method

**For End Users**: Use the ZIP package - it's the most compatible and easiest to use.

**For Professional Distribution**: 
1. Install Inno Setup (https://jrsoftware.org/isinfo.php)
2. Compile the `.iss` script to create a professional installer
3. Distribute the resulting `.exe` file

## Installation Instructions for Users

### ZIP Package Method:
1. Download and extract `FileTransferPro_v{self.app_version}_Installer.zip`
2. **Full Installation**: Right-click `install.bat` → "Run as administrator"
3. **Portable Mode**: Double-click `run_portable.bat`

### Professional Installer Method:
1. Download the installer `.exe` file
2. Double-click to run the installer
3. Follow the installation wizard
4. Launch from desktop shortcut or Start Menu

## System Requirements

- **OS**: Windows 10 or later (64-bit recommended)
- **RAM**: 512 MB minimum, 1 GB recommended
- **Disk**: 100 MB free space
- **Network**: Required for file transfers
- **Dependencies**: None (all included in package)

## Features Included

✅ **Standalone Executable** - No Python installation required
✅ **Modern GUI** - Professional, intuitive interface
✅ **Secure Transfers** - AES encryption for all data
✅ **Resume Capability** - Continue interrupted transfers
✅ **Real-time Monitoring** - Speed, progress, and ETA tracking
✅ **Multiple Files** - Send files and folders simultaneously
✅ **Cross-platform** - Compatible with Mac and Linux versions
✅ **No Size Limits** - Transfer files of any size

## Troubleshooting

**Installation Issues**:
- Run installer as Administrator
- Temporarily disable antivirus during installation
- Check Windows User Account Control settings

**Runtime Issues**:
- Allow through Windows Firewall
- Check network connectivity
- Verify IP addresses and ports

## Support

- **Documentation**: Included in `docs/` folder
- **User Guide**: `docs/user_guide.md`
- **Developer Guide**: `docs/developer_guide.md`

---

**Build Date**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
**Version**: {self.app_version}
**Platform**: Windows (64-bit)
"""
        
        guide_path = self.desktop / "Distribution_Guide.txt"
        try:
            with open(guide_path, 'w', encoding='utf-8') as f:
                f.write(guide_content)
            self.log(f"✅ Distribution guide created: {guide_path}")
            return guide_path
        except Exception as e:
            self.log(f"Failed to create distribution guide: {e}", "ERROR")
            return False
    
    def build_all_formats(self):
        """Build all available installer formats."""
        self.log(f"🚀 Creating all installer formats for {self.app_name} v{self.app_version}")
        self.log("=" * 80)
        
        created_files = []
        
        # 1. Create ZIP installer
        zip_file = self.create_zip_installer()
        if zip_file:
            created_files.append(("ZIP Package", zip_file))
        
        # 2. Create self-extracting installer (if 7-Zip available)
        sfx_file = self.create_self_extracting_installer()
        if sfx_file:
            created_files.append(("Self-Extracting Archive", sfx_file))
        
        # 3. Create Inno Setup script
        inno_script = self.create_inno_setup_script()
        if inno_script:
            created_files.append(("Inno Setup Script", inno_script))
        
        # 4. Create NSIS script
        nsis_script = self.create_nsis_script()
        if nsis_script:
            created_files.append(("NSIS Script", nsis_script))
        
        # 5. Create distribution guide
        guide = self.create_distribution_guide()
        if guide:
            created_files.append(("Distribution Guide", guide))
        
        # Summary
        self.log("🎉 All installer formats created successfully!")
        self.log("=" * 80)
        self.log("📦 Created Files on Desktop:")
        for name, path in created_files:
            self.log(f"  • {name}: {path.name}")
        
        self.log("\n📋 Next Steps:")
        self.log("  1. Test the ZIP package on a clean machine")
        self.log("  2. For professional installers, compile the .iss or .nsi scripts")
        self.log("  3. Distribute the appropriate format to your users")
        self.log("  4. Refer to Distribution_Guide.txt for detailed instructions")
        
        return len(created_files) > 0

def main():
    """Main entry point."""
    builder = CompleteInstallerBuilder()
    success = builder.build_all_formats()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
