#!/usr/bin/env python3
"""
Generated setup script for FileTransfer Pro MSI installer.
"""

import os
import sys
from cx_Freeze import setup, Executable

# Application metadata
APP_NAME = "FileTransfer Pro"
APP_VERSION = "2.0.0"
APP_DESCRIPTION = "Professional File Transfer Application"
APP_AUTHOR = "FileTransfer Team"
APP_GUID = "{12345678-1234-5678-9012-123456789012}"

# Build options
build_exe_options = {
    "packages": [
        "tkinter", "tkinter.ttk", "tkinter.filedialog", "tkinter.messagebox",
        "cryptography", "socket", "threading", "json", "pathlib", "logging",
        "argparse", "time", "datetime", "os", "sys", "shutil", "gzip", "zlib",
        "hashlib", "uuid", "queue", "collections", "functools", "itertools",
        "psutil", "tqdm", "ssl", "urllib", "http", "email", "base64"
    ],
    "excludes": ["test", "tests", "unittest", "pydoc", "doctest", "tkinter.test"],
    "include_files": [
        ("src/", "src/"),
        ("docs/", "docs/") if os.path.exists("docs") else None,
        ("README.md", "README.md") if os.path.exists("README.md") else None,
        ("requirements.txt", "requirements.txt") if os.path.exists("requirements.txt") else None
    ],
    "include_msvcrt": True,
    "optimize": 2,
    "zip_include_packages": ["*"],
    "zip_exclude_packages": [],
    "silent": True
}

# Remove None entries from include_files
build_exe_options["include_files"] = [f for f in build_exe_options["include_files"] if f is not None]

# MSI build options
bdist_msi_options = {
    "upgrade_code": APP_GUID,
    "add_to_path": False,
    "initial_target_dir": r"[ProgramFilesFolder]\FileTransfer Pro",
    "summary_data": {
        "author": APP_AUTHOR,
        "comments": APP_DESCRIPTION,
        "keywords": "file transfer, networking, secure, professional"
    },
    "target_name": "FileTransferPro_Setup.msi"
}

# Executable configuration
executables = [
    Executable(
        "main.py",
        base="Win32GUI",
        target_name="FileTransferPro.exe",
        shortcut_name="FileTransfer Pro",
        shortcut_dir="DesktopFolder"
    )
]

if __name__ == "__main__":
    setup(
        name=APP_NAME,
        version=APP_VERSION,
        description=APP_DESCRIPTION,
        author=APP_AUTHOR,
        options={
            "build_exe": build_exe_options,
            "bdist_msi": bdist_msi_options
        },
        executables=executables
    )
