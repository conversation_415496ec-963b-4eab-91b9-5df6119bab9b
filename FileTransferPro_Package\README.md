# File Transfer Application

A secure, fast, and user-friendly file transfer application that allows you to send large files between machines over a network without relying on paid cloud services.

## Features

- 🚀 **Fast TCP-based transfers** - Reliable file transfer with automatic chunking
- 🎨 **Intuitive GUI** - Easy-to-use interface with file picker and drag-drop
- 📊 **Real-time progress** - Progress bars, transfer speed, and ETA
- 🔒 **Secure transfers** - AES encryption for file content
- 📁 **Multiple files** - Queue and transfer multiple files simultaneously
- ⚡ **Resume capability** - Resume interrupted transfers
- 🌐 **Cross-platform** - Works on Windows, Mac, and Linux

## Quick Start

### Prerequisites
- Python 3.7 or higher
- Required packages (install with `pip install -r requirements.txt`)

### Installation

1. **Clone or download** this repository
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
3. **Run the application**:
   ```bash
   python main.py
   ```

### Running the Application

1. **GUI Mode** (Default - Recommended):
   ```bash
   python main.py
   ```

2. **Start as Server (Receiver)**:
   ```bash
   python main.py --mode server
   ```

3. **Start as Client (Sender)**:
   ```bash
   python main.py --mode client
   ```

4. **Run Demo**:
   ```bash
   python demo.py
   ```

### Quick Demo

To see the application in action, run the demo script:

```bash
python demo.py
```

This will create test files and demonstrate a complete file transfer between a local server and client.

## Project Structure

```
FileTransfer/
├── src/
│   ├── core/
│   │   ├── __init__.py
│   │   ├── server.py          # TCP server implementation
│   │   ├── client.py          # TCP client implementation
│   │   ├── protocol.py        # File transfer protocol
│   │   └── encryption.py      # Encryption/decryption utilities
│   ├── gui/
│   │   ├── __init__.py
│   │   ├── main_window.py     # Main GUI window
│   │   ├── progress_dialog.py # Progress tracking dialog
│   │   └── settings_dialog.py # Connection settings
│   └── utils/
│       ├── __init__.py
│       ├── file_utils.py      # File handling utilities
│       ├── network_utils.py   # Network utilities
│       └── logger.py          # Logging configuration
├── tests/
│   ├── test_core.py
│   ├── test_gui.py
│   └── test_utils.py
├── docs/
│   ├── user_guide.md
│   └── developer_guide.md
├── requirements.txt
├── main.py
└── README.md
```

## Architecture

The application follows a modular architecture:

- **Core Layer**: TCP socket implementation and file transfer protocol
- **GUI Layer**: User interface built with tkinter
- **Utils Layer**: Shared utilities for file handling, networking, and logging

## Usage Examples

### GUI Mode
1. Start the application: `python main.py`
2. Use the "Send Files" tab to connect to a server and send files
3. Use the "Receive Files" tab to start a server and receive files
4. Configure settings in the "Settings" tab

### Command Line Mode
```bash
# Start server on all interfaces, port 8888
python main.py --mode server --host 0.0.0.0 --port 8888

# Connect to server and send files (requires GUI for file selection)
python main.py --mode client --host ************* --port 8888
```

## Testing

Run the test suite to verify functionality:

```bash
# Run all tests
python -m pytest tests/ -v

# Run with coverage
python -m pytest tests/ --cov=src --cov-report=html
```

## Documentation

- **User Guide**: `docs/user_guide.md` - Complete user documentation
- **Developer Guide**: `docs/developer_guide.md` - Technical documentation for developers
- **Demo Script**: `demo.py` - Automated demonstration of core functionality

## Troubleshooting

### Common Issues

1. **Connection refused**: Check if server is running and firewall allows connections
2. **Permission denied**: Ensure write permissions for download directory
3. **Import errors**: Install dependencies with `pip install -r requirements.txt`

### Getting Help

- Check the user guide in `docs/user_guide.md`
- Review the troubleshooting section
- Run the demo script to verify installation: `python demo.py`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Acknowledgments

- Built with Python and tkinter for cross-platform compatibility
- Uses cryptography library for secure file transfers
- Inspired by the need for free, secure file transfer solutions
