@echo off
echo Installing FileTransfer Pro v2.0.0...

REM Create installation directory
set INSTALL_DIR=%ProgramFiles%\FileTransfer Pro
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy files
copy "FileTransferPro.exe" "%INSTALL_DIR%\"
copy "README.md" "%INSTALL_DIR%\" 2>nul
copy "requirements.txt" "%INSTALL_DIR%\" 2>nul
if exist "docs" xcopy "docs" "%INSTALL_DIR%\docs\" /E /I /Y

REM Create desktop shortcut
set DESKTOP=%USERPROFILE%\Desktop
echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP%\FileTransfer Pro.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\FileTransferPro.exe'; $Shortcut.Save()"

REM Create start menu entry
set STARTMENU=%ProgramData%\Microsoft\Windows\Start Menu\Programs
if not exist "%STARTMENU%\FileTransfer Pro" mkdir "%STARTMENU%\FileTransfer Pro"
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU%\FileTransfer Pro\FileTransfer Pro.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\FileTransferPro.exe'; $Shortcut.Save()"

REM Create uninstaller
echo @echo off > "%INSTALL_DIR%\uninstall.bat"
echo echo Uninstalling FileTransfer Pro... >> "%INSTALL_DIR%\uninstall.bat"
echo del "%DESKTOP%\FileTransfer Pro.lnk" 2^>nul >> "%INSTALL_DIR%\uninstall.bat"
echo rmdir /s /q "%STARTMENU%\FileTransfer Pro" 2^>nul >> "%INSTALL_DIR%\uninstall.bat"
echo rmdir /s /q "%INSTALL_DIR%" >> "%INSTALL_DIR%\uninstall.bat"
echo echo FileTransfer Pro has been uninstalled. >> "%INSTALL_DIR%\uninstall.bat"
echo pause >> "%INSTALL_DIR%\uninstall.bat"

echo.
echo FileTransfer Pro has been installed successfully!
echo.
echo Installation directory: %INSTALL_DIR%
echo Desktop shortcut: %DESKTOP%\FileTransfer Pro.lnk
echo.
echo You can now launch FileTransfer Pro from the desktop shortcut or Start Menu.
echo To uninstall, run: "%INSTALL_DIR%\uninstall.bat"
echo.
pause
